"""
**********************************************************************************************
------------------------ ALL PROMPTS FOR CHATGPT SHOULD BE SAVE HERE ------------------------
**********************************************************************************************
"""

def get_industry_icp_prompt_text() -> str:
    return """Website title: {title}
Website description: {description}
Website URL: {domain}

According to the details provided, please provide the industry category (less than 5 words) and a concise target audience description (less than 25 words) for the website.

{format_instructions}
"""


def get_competitors_prompt() -> str:
    """
    """
    return """Find competitors of {protocol}://{domain}.
{format_instructions}"""


def article_title_generation_prompt_text():
    return """Create the Content Plan as if you are an expert SEO professional.
website: "{domain}"
industry: "{industry}"
ideal customer profile: "{icp}"

Please return 1 long-tail blog title for each Keyword below by following the given Instructions.

Keywords:
{keywords_on_newline}

Instructions:
1. Maintain a counter and increment it by 1 after generating each blog title. Stop generating once this counter reaches {title_count}.
2. The titles should not contain colons.
3. Do not add anything before or after the blog titles.
4. Do not include any blog titles that are more than 10 words.
5. Use simple words in the suggested blog titles.
6. The titles should create curiosity within the user.
7. Do not include any blog titles that have more than 1 adjective.
8. Do not repeat the same title.

{format_instructions}
"""


def meta_description_generator_prompt():
    return """
You are a market research expert & copywriter. Act as a copywriter. Include human searcher intent. Write click-enticing clickbait meta description of minimum 150 characters for the following topic and the description must not exceed 160 characters. Avoid generic keywords. Use specific terms only. No quotes or explanations. Here is the keyword: "{keyword}"

Please provide the output in JSON format: [{{"keyword": "keyword", "meta description": "string"}}]

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_rewording_prompt():
    return """
You are an expert AI Rewording. Swiftly reword and rephrase sentences or paragraphs for posts, emails, or articles in fluent English of this sentence, paragraph or keyword and you'll assist me in generating a polished version to enhance the clarity and style of the content. Avoid generic keywords. Use specific terms only. No quotes or explanations. Here is the content: "{content}"

Please provide "{count}" variations in the output in JSON format: {{"content": "content", "rephrase": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_blog_title_generator_prompt():
    return """You are an expert AI Blog Title Generator. Generate engaging, SEO-friendly blog post titles to inspire a wide range of traffic-driving content.Generate a blog title of minimum 12 characters for the following blog description must not exceed 15 characters. Avoid generic keywords.Use specific terms only.No quotes or explanations. Here is the blog_description: "{blog_description}"
Please provide "{count}" titles in the output in JSON format: {{"blog_description": "blog_description", "titles": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_content_idea_generator_prompt():
    return """You are an expert content writer.Write engaging, SEO-friendly content idea. Content idea have minimum 30 characters for the following topic and must not exceed 100 characters. Avoid generic keywords. Use specific terms only. No quotes or explanations. Here is the title: "{title}"
Please provide "{count}" ideas in the output in JSON format: {{"title": "title", "ideas": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_social_media_bio_generator_prompt():
    return """You are an expert Social Media Bio Generator. Generate the perfect bio to add to my social media profiles without any of the heavy lifting. Bio have minimum 25 characters and must not exceed 50 characters. Avoid generic keywords. Use specific terms only.No quotes or explanations. Here is the profile description: "{description}"
Please provide "{count}" bio in the output in JSON format: {{"description": "description", "bio": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_social_media_caption_generator_prompt():
    return """You are an expert Social Media Caption Generator. Instantly create engaging, catchy captions for my social media posts.Caption have minimum 15 characters and must not exceed 30 characters. Avoid generic keywords.Use specific terms only. No quotes or explanations. Here is the topic: "{topic}"
Please provide "{count}" captions in the output in JSON format: {{"topic":"topic", "captions": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_social_media_hashtag_generator_prompt():
    return """You are an expert Social Media Hashtag Generator. Generate {count} hashtags for the given topic. Avoid generic keywords.Use specific terms only. No quotes or explanations. Here is the topic: "{topic}"
Please provide output in JSON format: {{"topic":"topic", "hashtags": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_social_media_username_generator_prompt():
    return """You are an expert Social Media Username Generator. Generate unique social media usernames effortlessly. Generate {count} usernames. Avoid generic keywords. Use specific terms only. No quotes or explanations. Here is the profile description: "{description}"
Please provide output in JSON format: {{"profile_description":"profile_description", "usernames": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_youtube_video_title_generator_prompt():
    return """You are an expert YouTube Video Title Generator. Generate engaging and eye-catching YouTube video titles. The title has a minimum of 20 characters and must not exceed 50. Avoid generic keywords. Use specific terms only. No quotes or explanations. Here is the short description of the video: "{description}"
Please provide "{count}" titles in the output in JSON format: {{"description": "description", "titles": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_brand_name_generator_prompt():
    return """You are an expert brand name generator. Generate {count} brand names. Avoid generic keywords. Use specific terms only. No quotes or  explanations. Here is the description of my brand: "{description}"
Please provide "{count}" names in the output in JSON format: {{"description": "description", "names": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_business_name_generator_prompt():
    return """You are an expert business name generator. Generate {count} business names for my business. Avoid generic keywords. No quotes or explanations. Here is the short description of my business: "{description}"
Please provide output in JSON format: {{"description": "description", "names": ["string", "string", "string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""

def ai_li_post_idea_generator():
    return """Create me {count} top-notch LinkedIn post ideas by analyzing this viral hook. Create a table - idea, new hook, tell why it will work, give a score, and give the psychology behind it. Here's the viral hook: {hook}.
Please provide output in JSON format: {{"hook": "hook", "ideas": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""

def ai_color_psychology_generator():
    return """Generate a set of {count} hex color codes, curating a palette designed to evoke {emotion}.
Please provide output in JSON format: {{"emotion": "emotion", "color_codes": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_job_desc_generator_prompt():
    return """Generate {count} job descriptions for the role of {job_title}.
Each job description should include key responsibilities, required qualifications, and any specific skills or experience desired for the given job role.
Please provide output in JSON format: {{"job_title": "job_title", "job_descriptions": [{{"responsibilities":"string", "qualifications":"string", "skills_experience":"string"}}, {{"responsibilities":"string", "qualifications":"string", "skills_experience":"string"}},
{{"responsibilities":"string", "qualifications":"string", "skills_experience":"string"}}]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_story_generator_prompt():
    return """You are an expert story writer. Generate a captivating story. It should has {paragraphs} paragraphs. Ensure that the narrative is engaging, with a well-developed plot, interesting characters, and a satisfying conclusion. Here is the topic of my story: "{story_topic}"
Please provide output in JSON format: {{"story_topic": "story_topic", "story": ["sting"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_business_desc_prompt():
    return """Business Name: {business_name}
Business Description: {short_description}
Number of Descriptions: {count}
Generate descriptions for my business. Description must have a minimum 150 characters and maximum 200 characters.
Please provide output in JSON format: {{"business_name": "business_name", "short_description": "short_description",  "descriptions": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_business_slogan_generator_prompt():
    return """
Business Name: {business_name}
Business Description: {short_description}
Number of Slogans: {count}
Generate Slogans for my business.Avoid generic keywords. Use specific terms only. The slogan must have a minimum 15 characters and maximum 30 characters.
Please provide output in JSON format: {{"business_name": "business_name", "short_description": "short_description",  "slogans": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_nda_generator_prompt():
    return """Generate Non-Disclosure Agreements (NDAs) between {disclosing_party} and {receiving_party}. Ensure that each NDA includes standard confidentiality clauses, duration, and any specific terms provided.It should contain words in between 500-1000.
Please provide output in JSON format: {{"disclosing_party": "disclosing_party", "receiving_party":"receiving_party" "NDA": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_essay_generator_prompt():
    return """Essay Topic: {essay_topic}
Number of Paragraphs: {paragraphs}
Instructions: Provide a comprehensive essay on the given topic.Ensure that the essay is well-structured with an introduction, body paragraphs, and a conclusion.Include relevant information, facts, and examples to support the arguments.It should contain words in between 500-1000.
Please provide output in JSON format: {{"essay_topic": "essay_topic", "essay": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_keyword_generator_prompt():
    return """Given the keyword '{keyword}', generate {count} related keywords that are closely associated or relevant. Consider synonyms, related terms, and concepts to provide a comprehensive set of keywords that can enhance the user's understanding or broaden their search scope.
Please provide output in JSON format: {{"keyword": "keyword", "keywords": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_video_script_generator_prompt():
    return """Generate a compelling video script based on the following description: "{short_story}".
Include key points, engaging dialogue, and transitions to create a dynamic and informative video. The target duration for the video is {duration}. Ensure that the script flows seamlessly and captivates the audience's attention from start to finish. Feel free to add relevant details, anecdotes, and a call-to-action to enhance the overall impact of the video.
Please provide output in JSON format: {{"short_story": "short_story", "script": ["script"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_blog_meta_desc_generator_prompt():
    return """You are an expert in generating meta-description. Generate {count} meta-descriptions for my blog. It should have a minimum of 150 characters and must not exceed 160 characters.Here is the title of my blog: {title}.
Please provide output in JSON format: {{"title": "title", "meta-descriptions": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output
"""


def ai_article_generator_prompt():
    return """Title: {title}
Generate {count} outlines for my article based on the title. Avoid generic keywords. Use specific terms only. No quotes or explanations.
Please provide the output in JSON format: {{"title": "title", "outlines": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output"""


def ai_company_bio_generator_prompt():
    return """Company Name: {company_name}
Company Description: {company_description}
Generate bio for my company based on of input data. Bio should not exceed 150 characters.
Please provide {count} bios in the output in JSON format: {{"company_name": "company_name", "company_description": "company_description", "bios": ["string"]}}

**NOTE**: Do not add anything before or after the JSON output"""


def get_related_keywords_prompt():
    return """You are an expert SEO professional. Give me 35 new SEO keywords that I should target based on the below keyword.
"{keyword}"
"The keywords should be written in the same language as the input keyword."

{format_instructions}
"""


def segmind_ai_img_context_prompt_text(template_id: str):
    # ---------- Neon style (with text) ----------
    if template_id == "neon-style-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a neon design style. The OUTPUT should be in the following format only:

Neon design style Blog Post Featured image with bright, glowing elements on a dark background. In the center, bold, glowing text reads {blog_title} in a neon color that stands out against the dark backdrop. Surrounding the text, neon-style illustrations represent key ideas related to {blog_title}: [3 descriptive elements related to the title]. Use bright, neon colors with glowing effects and subtle light flares. Ensure the design creates a vibrant, eye-catching effect, reminiscent of neon signs.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Neon style (without text) ----------
    elif template_id == "neon-style-without-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a neon design style. The OUTPUT should be in the following format only:

Neon design style Blog Post Featured image with bright, glowing elements on a dark background. Abstract representations of concepts related to {blog_title} without any text. Use neon-style illustrations to depict key ideas: [3 descriptive elements related to the title]. Use bright, neon colors with glowing effects and subtle light flares. Ensure the design creates a vibrant, eye-catching effect, reminiscent of neon signs.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Water color (with text) ----------
    elif template_id == "water-color-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a watercolor design style. The OUTPUT should be in the following format only:

Watercolor style Blog Post Featured image with soft, painterly effects on a textured paper background. In the center, elegant, hand-lettered text reads {blog_title} in a color that complements the watercolor palette, with subtle watercolor bleed effects. Surrounding the text, soft watercolor illustrations represent key ideas related to {blog_title}: [3 descriptive elements related to the title]. Use a color palette with blended hues and gentle gradients. Ensure the design includes delicate textures, with areas of the paper showing through and soft color transitions.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Water color (without text) ----------
    elif template_id == "water-color-without-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a watercolor design style. The OUTPUT should be in the following format only:

Watercolor style Blog Post Featured image with soft, painterly effects on a textured paper background. Abstract representations of concepts related to {blog_title} without any text. Use soft watercolor illustrations to depict key ideas: [3 descriptive elements related to the title]. Use a color palette with blended hues and gentle gradients. Ensure the design includes delicate textures, with areas of the paper showing through and soft color transitions.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Retro (with text) ----------
    elif template_id == "retro-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a retro/vintage design style. The OUTPUT should be in the following format only:

Retro/vintage design style Blog Post Featured image with elements inspired by past decades, such as the 80s or 90s. In the center, bold, nostalgic text reads {blog_title} in a classic retro font with a vintage color palette. Surrounding the text, retro-style illustrations represent key ideas related to {blog_title}: [3 descriptive elements related to the title]. Use vibrant, nostalgic colors with geometric patterns or classic textures reminiscent of the era. Ensure the design evokes a sense of nostalgia with distinct retro elements and styling.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Retro (without text) ----------
    elif template_id == "retro-without-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a retro/vintage design style. The OUTPUT should be in the following format only:

Retro/vintage design style Blog Post Featured image with elements inspired by past decades, such as the 80s or 90s. Abstract representations of concepts related to {blog_title} without any text. Use retro-style illustrations to depict key ideas: [3 descriptive elements related to the title]. Use vibrant, nostalgic colors with geometric patterns or classic textures reminiscent of the era. Ensure the design evokes a sense of nostalgia with distinct retro elements and styling.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Comic style (with text) ----------
    elif template_id == "comic-style-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a comic book design style. The OUTPUT should be in the following format only:

Comic book design style Blog Post Featured image with bold outlines and vibrant colors. In the center, dynamic, comic-style text reads {blog_title} with dramatic effects, such as speech bubbles or action words. Surrounding the text, comic book illustrations represent key ideas related to {blog_title}: [3 descriptive elements related to the title]. Use bright, contrasting colors and halftone patterns to mimic classic comic art. Ensure the design includes bold outlines, expressive visuals, and a playful, energetic vibe.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Comic style (without text) ----------
    elif template_id == "comic-style-without-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a comic book design style. The OUTPUT should be in the following format only:

Comic book design style Blog Post Featured image with bold outlines and vibrant colors. Abstract representations of concepts related to {blog_title} without any text. Use comic book illustrations to depict key ideas: [3 descriptive elements related to the title]. Use bright, contrasting colors and halftone patterns to mimic classic comic art. Ensure the design includes bold outlines, expressive visuals, and a playful, energetic vibe.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Doodle sketch (with text) ----------
    elif template_id == "doodle-sketch-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a doodle/sketch design style. The OUTPUT should be in the following format only:

Doodle/sketch design style Blog Post Featured image with a hand-drawn look, featuring simple lines and shading. In the center, playful, handwritten text reads {blog_title} integrated into the sketch. Surrounding the text, doodle-style illustrations represent key ideas related to {blog_title}: [3 descriptive elements related to the title]. Use a monochromatic or limited color palette with simple shading to emphasize the hand-drawn aesthetic. Ensure the design has a casual, creative feel with rough, organic lines and minimalistic details.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Doodle sketch (without text) ----------
    elif template_id == "doodle-sketch-without-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a doodle/sketch design style. The OUTPUT should be in the following format only:

Doodle/sketch design style Blog Post Featured image with a hand-drawn look, featuring simple lines and shading. Abstract representations of concepts related to {blog_title} without any text. Use doodle-style illustrations to depict key ideas: [3 descriptive elements related to the title]. Use a monochromatic or limited color palette with simple shading to emphasize the hand-drawn aesthetic. Ensure the design has a casual, creative feel with rough, organic lines and minimalistic details.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Grunge (with text) ----------
    elif template_id == "grunge-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a grunge design style. The OUTPUT should be in the following format only:

Grunge design style Blog Post Featured image with textured, distressed elements and a rough, edgy feel. In the center, the text {blog_title} is displayed with a distressed, gritty font. Surround the text with grunge-inspired elements such as splatters, scratches, and rough textures. Use a color palette with dark, muted tones like black, gray, and deep [based on blog title's industry style] to enhance the raw and rugged [find the title niche] appearance.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Grunge (without text) ----------
    elif template_id == "grunge-without-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a grunge design style. The OUTPUT should be in the following format only:

Grunge design style Blog Post Featured image with textured, distressed elements and a rough, edgy feel. Abstract representations of concepts related to {blog_title} using grunge-inspired elements like splatters, scratches, and rough textures. Use a color palette with dark, muted tones like black, gray, and deep [based on blog title's industry style] to enhance the raw and rugged [find the title niche] appearance.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Water Color With Doodle (with text) ----------
    elif template_id == "water-color-with-doodle-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a Watercolor and doodle/sketch design style. The OUTPUT should be in the following format only:

Watercolor and doodle/sketch design style Blog Post Featured image on a textured white paper background. In the center, playful, handwritten text reads {blog_title} integrated into the sketch. Surround the text with abstract representations of concepts related to {blog_title} using a combination of soft watercolor elements and doodle-style sketches. Illustrate[3 descriptive elements related to the title] with gentle gradients, subtle bleeding effects, and simple shading. Include areas where the white paper shows through and add a few splatter effects in the background for texture. The overall design should have a casual, creative feel with rough, organic lines and minimalistic details, harmoniously blending both styles.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Water Color With Doodle (without text) ----------
    elif template_id == "water-color-with-doodle-without-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a Watercolor and doodle/sketch design style. The OUTPUT should be in the following format only:

Watercolor and doodle/sketch design style Blog Post Featured image on a textured white paper background. Abstract representations of concepts related to {blog_title} are illustrated using a combination of soft watercolor elements and doodle-style sketches. Depict [3 descriptive elements related to the title] with gentle gradients, subtle bleeding effects, and simple shading. Include areas where the white paper shows through and add a few splatter effects in the background for texture. The design should have a casual, creative feel with rough, organic lines and minimalistic details, blending both styles seamlessly.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Cyberpunk (with text) ----------
    elif template_id == "cyberpunk-with-text":
        prompt = """Given the title {blog_title} generate the following details for creating a featured image with text in a cyberpunk design style. The OUTPUT should be in the following format only:

Cyberpunk design style Blog Post Featured image with futuristic elements and glitch effects. In the center, bold, neon-style text reads {blog_title} with subtle digital distortions. Use neon colors like electric blue and bright pink against a dark background. Include minimal tech-inspired elements, such as simple circuit patterns or digital artifacts.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    # ---------- Cyberpunk (without text) ----------
    else:
        prompt = """Given the title {blog_title} generate the following details for creating a featured image without text in a cyberpunk design style. The OUTPUT should be in the following format only:

Cyberpunk design style Blog Post Featured image with futuristic elements and glitch effects. Abstract representations of concepts related to {blog_title} with minimal tech-inspired elements. Use neon colors like electric blue and bright pink against a dark background. Include subtle digital distortions or simple circuit patterns.

Provide the specific details for each category to create an optimal visual representation.

Instructions:
1. Don't add any  #, markdown, *, etc.
2. Do not add anything before or after the textual description of the image.
"""

    return prompt


def rephrase_article_title_prompt_text():
    return """
Title: "{blog_title}"

Shorten this to {words} words in {language} do not add any special character.

{format_instructions}
"""


def get_programmatic_seo_prompt_text():
    return """
Create a list of exactly {no_of_titles} **unique** programmatic SEO titles based on the following pattern:

Pattern:
{pattern}

Examples to guide format and creativity:
{example_pattern}

Strict instructions:
- Ensure the output has exactly {no_of_titles} titles.
- Do not output fewer than {no_of_titles}.

{format_instructions}
"""

def get_glossary_words_prompt_text():
    return """
create a list of {no_of_words} glossary words for "{word}".
Give only words in json format.

{format_instructions}
"""

def get_glossary_contents_prompt_text():
    return """
{topic} Glossary.
Explain & Define: {term} (Exclude the label 'Definition:'and all texts before it in the response,
but retain other labels such as 'Characteristics' and 'Examples.' along with bullet points in bold)
Give examples if relevant

Important:
- Ensure the output is strictly in the "{language}" language and dialect.
- Use common phrases and terms of the specified language and dialect in the output.
- Avoid using em-dashes (—) in the content. Prefer commas, parentheses, or colons for separation instead.
"""


def deepinfra_ai_img_context_prompt_text():
    return """Blog Title: "{blog_title}"
What is the best visual representation in 2 sentences for the above topic with the help of an illustration?
Make sure to avoid using any text, arrows, textual boards, signs, flow charts, diagrams or specific examples in the illustration.

{format_instructions}
"""


def generate_html_calculator_prompt_text():
    return """Create an HTML {calculator_instruction} calculator using bootstrap.

Calculator Description: {calculator_description}

Output only the code. Don't wrap the html around markdown format or add newline characters(\\n). Write without any head or html page tag. Use inline css & js

{format_instructions}
"""


def modify_html_calculator_prompt_text():
    return """Conversation history with previous calculator code and modifications:
{conversation_history}

Please modify the calculator according to these new instructions: {modifications}

Output only the complete HTML code. Don't wrap the html around markdown format.
"""

def modify_article_content_using_custom_prompt_text():
    return """Modify the content of the article based on the provided instructions. Ensure that the core meaning and structure of the article remain intact. Make only minor adjustments to improve clarity, flow, or style without deviating significantly from the original content.

Article Content: {article_content}

Instructions: {instructions}

{format_instructions}
"""

def table_of_content_prompt_text():
    return """You are an expert in Markdown content formatting. Your task is to generate a structured Table of Contents (ToC) for the given article.

Article Content:
{complete_article_markdown}

Instructions:
- Language: Ensure the output is strictly in {language}.
- Clarity: Use simple words, avoid jargon, and be concise.
- Do NOT include any random heading out of the given article or heading that looks like a Table of Contents heading itself.
- Make sure to add the headings from the given article no random ToC.
- Do Not add serial number to heading.
- Headings Extraction:
  - Identify all Markdown headings (`#`, `##`, `###`, etc.).
  - Maintain a proper hierarchical structure.
- Formatting Requirements:
  - Return only a valid JSON object with clickable anchor links (e.g., `"link": "#heading-text"`).
  - Do not include any extra commentary or text before or after the JSON.
  - Do not add newline characters (`\\n`) at the start or end of the JSON.

Now, generate the JSON output strictly following the format below:
{format_instructions}
"""

def interlinking_prompt_text():
    return """Blog title: {term}

Blog article content:

{content}

In the above blog article, list the {count} most important phrases that can be linked to another article for SEO purposes.
- Selected phrases should have more than one word but less than ten.
- Avoid selecting the identical phrase multiple times.
- Do not add anything before or after each phrase.
- Phrases should only contain text.
- Just pick the exact phrase from the article and do not add any extra words.
- Do not include any aesthetically pleasing elements like bullet points, numbered lists, headings, or unnecessary capitalization in the phrase.
- Do not pick more than one phrase from the same paragraph or group of paragraphs.

Strictly follow the instructions below for output formatting
{format_instructions}
"""
