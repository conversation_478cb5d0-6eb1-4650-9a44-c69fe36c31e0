import os
import json
import time
import logging
import kubernetes
import requests
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed

from django.urls import reverse
from django.db.models import QuerySet
from django.core.management.base import BaseCommand

from mainapp.models import ArticleInter<PERSON>LinkQueue, KubernetesJob
from mainapp.utils import generate_k8_job_id, get_redis_connection, create_k8_job
from mainapp.tasks import celery_check_flyio_provisioning
from AbunDRFBackend.settings import (K8_JOB_RETRIES, REDIS_TASK_DATA_DB, REDIS_ART_GEN_EXPIRY, DEBUG, FLY_API_HOST,
                                     FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN, FLY_ARTICLE_INTERNAL_LINK_APP_NAME,
                                     FLY_ARTICLE_INTERNAL_LINK_IMAGE_URL)

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Process queued article internal linking tasks (K8s for DEBUG=True, Fly.io for DEBUG=False)'
    DELAY = 60  # Delay between processing batches (in seconds)
    BATCH_SIZE = 50  # Number of tasks to process in each batch
    MAX_CONCURRENT_JOBS = 10  # Maximum number of concurrent jobs for article internal linking (K8s or Fly.io)

    def handle(self, *args, **kwargs):
        self.stdout.write('Starting article internal link queue processing...')

        try:
            self.process_queue()
        except Exception as e:
            logger.critical(f"Error processing article internal link queue: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error processing article internal link queue: {str(e)}'))

    def process_queue(self):
        """Process the article internal link queue continuously"""
        while True:
            # Get queued article internal link tasks
            queued_tasks: QuerySet[ArticleInternalLinkQueue] = ArticleInternalLinkQueue.objects.filter(
                status='queued'
            ).order_by('created_at')[:self.BATCH_SIZE]

            if not queued_tasks.exists():
                self.stdout.write('No queued article internal link tasks found. Waiting...')
                time.sleep(self.DELAY)
                continue

            # Check current running article internal linking jobs count
            current_jobs = self.get_running_article_internal_link_jobs_count()
            if current_jobs >= self.MAX_CONCURRENT_JOBS:
                self.stdout.write(f'Maximum concurrent article internal linking jobs ({self.MAX_CONCURRENT_JOBS}) reached. Waiting...')
                time.sleep(self.DELAY)
                continue

            # Process tasks in parallel
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = []
                for queue_item in queued_tasks:
                    future = executor.submit(self.process_article_internal_link_task, queue_item)
                    futures.append(future)

                # Wait for all tasks to complete
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        if result:
                            self.stdout.write(self.style.SUCCESS(f'Successfully processed article internal link task'))
                        else:
                            self.stdout.write(self.style.WARNING(f'Failed to process article internal link task'))
                    except Exception as e:
                        logger.error(f"Error processing article internal link task: {str(e)}")
                        self.stdout.write(self.style.ERROR(f'Error processing article internal link task: {str(e)}'))

            # Wait before processing next batch
            time.sleep(self.DELAY)

    def get_running_flyio_machines(self) -> List[Dict]:
        """Get current running machines from Fly.io for article internal linking"""
        try:
            response = requests.get(
                f"{FLY_API_HOST}/apps/{FLY_ARTICLE_INTERNAL_LINK_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                },
                timeout=30
            )

            if response.status_code == 200:
                machines = response.json()
                return [m for m in machines if m['state'] == 'started']
            else:
                logger.error(f"Failed to get Fly.io machines: {response.text}")
                return []

        except Exception as e:
            logger.error(f"Error getting Fly.io machine list: {e}")
            return []

    def get_running_article_internal_link_jobs_count(self) -> int:
        """Get the current number of running article internal linking jobs (K8s or Fly.io based on DEBUG setting)"""
        if DEBUG:
            # Use Kubernetes for development/staging
            try:
                with kubernetes.client.ApiClient() as api_client:
                    batch_v1_api = kubernetes.client.BatchV1Api(api_client)
                    namespace = os.environ['K8_NAMESPACE']

                    # List all jobs with the article internal_link category label
                    jobs = batch_v1_api.list_namespaced_job(
                        namespace=namespace,
                        label_selector='category=article_internal_link'
                    )

                    # Count jobs that are currently running (not completed or failed)
                    running_jobs = 0
                    for job in jobs.items:
                        if job.status.active and job.status.active > 0:
                            running_jobs += 1

                    return running_jobs
            except Exception as e:
                logger.error(f"Failed to get running article internal link K8s jobs count: {str(e)}")
            return 0

        else:
            # Use Fly.io for production
            try:
                running_machines = self.get_running_flyio_machines()
                return len(running_machines)
            except Exception as e:
                logger.error(f"Failed to get running article internal link Fly.io machines count: {str(e)}")

            return 0

    def process_article_internal_link_task(self, queue_item: ArticleInternalLinkQueue) -> bool:
        """Process a single article internal link task"""
        try:
            article = queue_item.article
            user = article.user

            # Mark queue item as processing
            queue_item.status = 'processing'
            queue_item.save()

            # Generate job ID for article internal linking
            article_internal_link_job_id = generate_k8_job_id('articleinternallink', username=user.username)

            try:
                article_internal_link_keywords = json.loads(queue_item.internal_link_keywords)
            except json.JSONDecodeError:
                article_internal_link_keywords = []

            # Prepare internal link data
            article_internal_link_data = {
                'domain_id': article.website.id,
                'article_uid': article.article_uid,
                'article_job_id': queue_item.k8_job.job_id,
                'internal_link_keywords': article_internal_link_keywords,
                'abun_webhook_url': reverse('wh-k8-article-internal-link'),
                'article_language_preference': user.article_language_preference,
                'max_internal_backlinks': user.max_internal_backlinks,
            }

            # Store data in Redis
            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.set(article_internal_link_job_id, json.dumps(article_internal_link_data))
                redis_connection.expire(article_internal_link_job_id, REDIS_ART_GEN_EXPIRY)

            if DEBUG:
                # Use Kubernetes for development/staging
                job_created = create_k8_job(
                    article_internal_link_job_id,
                    'article_internal_link',
                    article_internal_link_job_id,
                    user.id,
                    [article_internal_link_job_id]
                )

                if not job_created:
                    logger.error(f"Failed to create article internal link Kubernetes job for {article.article_uid}")
                    queue_item.status = 'failed'
                    queue_item.error_message = "Failed to create Kubernetes job due to cluster load"
                    queue_item.save()
                    return False

                logger.info(f"Article internal link task {article.article_uid} submitted to Kubernetes job {article_internal_link_job_id}")
                self.stdout.write(self.style.SUCCESS(f"Task {article.article_uid} submitted to Kubernetes job {article_internal_link_job_id}"))

            else:
                # Use Fly.io for production
                # Create Fly.io machine to process article internal linking
                cmd = f"python3 article_internal_link_generation.py {article_internal_link_job_id}"
                cmd = cmd.split()
                worker_props = {
                    "config": {
                        "image": FLY_ARTICLE_INTERNAL_LINK_IMAGE_URL,
                        "auto_destroy": True,
                        "init": {
                            "cmd": cmd
                        },
                        "restart": {
                            "policy": "on-failure",
                            "max_retries": K8_JOB_RETRIES
                        },
                        "guest": {
                            "cpu_kind": "shared",
                            "cpus": 1,
                            "memory_mb": 1024
                        }
                    },
                }

                res = requests.post(
                    f"{FLY_API_HOST}/apps/{FLY_ARTICLE_INTERNAL_LINK_APP_NAME}/machines",
                    headers={
                        'Authorization': f"Bearer {FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN}",
                        'Content-Type': 'application/json'
                    },
                    json=worker_props
                )

                if res.status_code != 200:
                    logger.error(f"Failed to send article internal link task to Fly.io: {res.text}")
                    queue_item.status = 'failed'
                    queue_item.error_message = f"Failed to send task to Fly.io: {res.text}"
                    queue_item.save()
                    return False

                # Get machine ID and start provisioning check
                machine_id = res.json()['id']

                # Run the celery flyio provisioning task
                celery_check_flyio_provisioning.delay(machine_id,
                                                      article_internal_link_job_id,
                                                      FLY_ARTICLE_INTERNAL_LINK_APP_NAME,
                                                      FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN)

                logger.info(f"Article internal link task {article.article_uid} submitted to Fly.io machine {machine_id}")
                self.stdout.write(self.style.SUCCESS(f"Task {article.article_uid} submitted to Fly.io machine {machine_id}"))

            return True

        except Exception as e:
            logger.error(f"Error processing article internal link task {queue_item.id}: {str(e)}")
            queue_item.status = 'failed'
            queue_item.error_message = str(e)
            queue_item.save()
            return False
