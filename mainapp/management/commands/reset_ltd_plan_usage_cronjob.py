import logging

from django.core.management.base import BaseCommand
from django.utils import timezone

from mainapp.models import AppSumoLicense
from mainapp.utils import get_next_renewal_date


logger = logging.getLogger(__name__)

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        current_time = timezone.now()
        logger.info(f"Starting usage counter reset at {current_time}")

        licenses = AppSumoLicense.objects.filter(
            license_status='active',
            next_renewal_date__lte=current_time
        )

        reset_count = 0

        for license in licenses:
            try:
                user = license.user
                if user:
                    # Store old values for logging
                    old_values = {
                        'articles': user.articles_generated,
                        'keywords': user.keywords_generated,
                        'titles': user.titles_generated,
                        'email': user.blog_emails_found,
                        'old_renewal_date': license.next_renewal_date
                    }

                    # Reset counters
                    user.articles_generated = 0
                    user.keywords_generated = 0
                    user.titles_generated = 0
                    user.blog_emails_found = 0

                    # Update next renewal date
                    license.next_renewal_date = get_next_renewal_date()

                    # Save changes
                    user.save()
                    license.save()

                    reset_count += 1

                    self.stdout.write(
                        f"Reset counters for '{user.email}' (License: {license.license_key}).\n"
                        f"Old values - Articles: {old_values['articles']},\n"
                        f"Keywords: {old_values['keywords']},\n"
                        f"Titles: {old_values['titles']}.\n"
                        f"Emails: {old_values['email']}.\n" 
                        f"Next renewal date updated from {old_values['old_renewal_date']}\n"
                        f"to {license.next_renewal_date}\n\n"
                    )

                else:
                    logger.error(
                        f"License key '{license.license_key}' is not associated with any user account.\n"
                        f"License status: {license.license_status},\n"
                        f"Created on: {license.created_on},\n"
                        f"Next renewal date: {license.next_renewal_date}\n\n"
                    )

            except Exception as e:
                logger.critical(f"Error processing license {license.license_key}: {str(e)}")

        self.stdout.write(
            self.style.SUCCESS(f'Successfully processed {reset_count} licenses')
        )
