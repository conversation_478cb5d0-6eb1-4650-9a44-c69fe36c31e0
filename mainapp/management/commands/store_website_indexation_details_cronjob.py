"""
(NOT IN USE CURRENTLY)
====================================================================================================================
This script will be run periodcally using cronjob to fetch and store all the indexation details for a website.
====================================================================================================================
"""
import itertools
from typing import Dict, List, Tuple
from concurrent.futures import ThreadPoolExecutor
from urllib3.exceptions import SS<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Error, ConnectionError

from django.core.management import BaseCommand
from django.db.models import QuerySet

from google.oauth2.credentials import Credentials
from google.auth.exceptions import RefreshError
from googleapiclient.errors import HttpError

from mainapp.models import User, WebsiteIndexation, Website
from mainapp.quickindex import sitemaps as fetch_sitemaps
from mainapp.email_messages import website_indexation_process_completed_body
from mainapp.utils import send_email
from AbunDRFBackend.settings import logging, ABUN_NOTIFICATION_EMAIL
from mainapp.google_integration_utils import (get_google_oauth2_credentials, get_site_name_on_gsc,
                                              fetch_sitemap_information, fetch_url_inspection_details)


logger = logging.getLogger(__file__)


class Command(BaseCommand):
    QPD = 2000  # queries per day for URL inspection (calls querying the same site)
    QPM = 600   # queries per minute for URL inspection (calls querying the same site)

    @staticmethod
    def all_indexed_pages_stored(indexation_details: List[Dict] | None) -> Tuple[bool, int]:
        """
        Checks if all pages are indexed
        :param indexation_details: Indexation data
        """
        if not indexation_details:
            return True, 0

        for idx, page in enumerate(indexation_details):
            if page.get('indexed') is None:
                return False, idx

        return True, 0

    @staticmethod
    def all_indexed_pages_updated(indexation_details: List[Dict] | None) -> Tuple[bool, int]:
        """
        Checks if all pages are updated
        :param indexation_details: Indexation data
        """
        if not indexation_details or indexation_details[0].get('updated') is None:
            return True, 0

        for idx, page in enumerate(indexation_details):
            updated: bool | None = page.get('updated')
            if not updated:
                return False, idx

        return True, 0

    def fetch_website_urls(self, credentials: Credentials, site: str) -> List:
        """
        Fetches all the urls of the website
        :param credentials: Google oauth2 credentials
        :param site: site name on GSC
        """
        self.stdout.write("[*] Fetching URLs\n")
        res = fetch_sitemap_information(credentials, site)
        all_urls = []

        if res:
            sitemaps = res['sitemap']

            for sitemap in sitemaps:
                sitemap_url = sitemap['path']
                urls = fetch_sitemaps.fetch_urls_from_sitemap_recursive(sitemap_url)
                all_urls.extend(urls)

            # remove duplicate urls using set
            unique_urls = list(set(all_urls))
            self.stdout.write(f"[*] {len(all_urls)} URLs found in sitemap.\n")
            return unique_urls

        return []

    def store_indexing_details(self, credentials: Credentials, site: str, website_pages: List,
                               website_indexation: WebsiteIndexation):
        """
        Stores indexing details to WebsiteIndexation model
        :param credentials: Google oauth2 credentials
        :param site: Connected website URL
        :param website_pages: All fetched pages
        :param website_indexation: WebsiteIndexation instance
        """
        self.stdout.write(f"[*] Storing index data start for '{website_indexation.website.domain}'\n")

        indexation_details: List[Dict] = []
        sliced_website_pages = website_pages[:self.QPD]
        total_pages = len(sliced_website_pages)
        executor = ThreadPoolExecutor(max_workers=min(max(1, self.QPM // 12), total_pages))

        if website_indexation.indexation_details:
            stored_indexation_details: List[Dict] = website_indexation.indexation_details
            all_pages = [page['url'] for page in stored_indexation_details]
        else:
            stored_indexation_details = None
            all_pages = []

        self.stdout.write(f"[*] Inspecting {total_pages} URLs.\n")

        for res, url in executor.map(fetch_url_inspection_details, itertools.repeat(credentials),
                                     itertools.repeat(site), sliced_website_pages):
            if not res:
                self.stdout.write(f"[X] Failed to inspect '{url}'\n")
                continue

            if res['inspectionResult']['indexStatusResult']['coverageState'] == "Submitted and indexed":
                indexation_details.append({
                    'url': url,
                    'indexed': True,
                    'sent_for_indexing': False,
                    'sent_on': ""
                })
            else:
                if url in all_pages:
                    indexation_details.append({
                        'url': url,
                        'indexed': False,
                        'sent_for_indexing': stored_indexation_details[all_pages.index(url)].get('sent_for_indexing', False),
                        'sent_on': stored_indexation_details[all_pages.index(url)].get('sent_on', "")
                    })
                else:
                    indexation_details.append({
                        'url': url,
                        'indexed': False,
                        'sent_for_indexing': False,
                        'sent_on': ""
                    })

        # store the remaining url
        sliced_website_pages = website_pages[self.QPD:]
        self.stdout.write(f"[*] Saving remaining {len(sliced_website_pages)} URLs\n")
        for page in sliced_website_pages:
            indexation_details.append({
                'url': page
            })

        # store the details in WebsiteIndexation
        website_indexation.indexation_details = indexation_details
        website_indexation.save()

    def continue_store_indexing_details(self, credentials: Credentials, site: str, website_pages: List,
                                        website_indexation: WebsiteIndexation, start_from: int):
        """
        Continue sotring the indexing details to WebsiteIndexation model
        :param credentials: Google oauth2 credentials
        :param site: Connected website URL
        :param website_pages: All fetched pages
        :param website_indexation: WebsiteIndexation instance
        :param start_from: Index to start from
        """
        self.stdout.write(f"[*] Storing index data continue for '{website_indexation.website.domain}'\n")

        indexation_details: List[Dict] = website_indexation.indexation_details
        sliced_website_pages = website_pages[start_from:(start_from + self.QPD)]
        total_pages = len(sliced_website_pages)
        executor = ThreadPoolExecutor(max_workers=min(max(1, self.QPM // 12), total_pages))

        self.stdout.write(f"[*] Inspecting {total_pages} URLs.\n")

        for res, url in executor.map(fetch_url_inspection_details, itertools.repeat(credentials),
                                     itertools.repeat(site), sliced_website_pages):
            if not res:
                self.stdout.write(f"[X] Failed to inspect '{url}'\n")
                continue

            if res['inspectionResult']['indexStatusResult']['coverageState'] == "Submitted and indexed":
                indexation_details[start_from]['url'] = url
                indexation_details[start_from]['indexed'] = True
                indexation_details[start_from]['sent_for_indexing'] = False
            else:
                indexation_details[start_from]['url'] = url
                indexation_details[start_from]['indexed'] = False

            start_from += 1

        website_indexation.indexation_details = indexation_details
        website_indexation.save()

    def update_indexing_details(self, credentials: Credentials, site: str, website_pages: List,
                                website_indexation: WebsiteIndexation):
        """
        Update the stored indexing details
        :param credentials: Google oauth2 credentials
        :param site: Connected website URL
        :param website_pages: All fetched pages
        :param website_indexation: WebsiteIndexation instance
        :param start_from: Index to start from
        """
        indexed_pages_details: List[Dict] = website_indexation.indexation_details
        indexed_page_urls: List = [page['url'] for page in indexed_pages_details]

        # Add new website pages
        for url in website_pages:
            if url not in indexed_page_urls:
                indexed_page_urls.append(url)
                indexed_pages_details.append({
                    'url': url,
                    'updated': False
                })

        all_pages_updated, start_from = self.all_indexed_pages_updated(indexed_pages_details)

        if all_pages_updated:
            self.stdout.write(f"[*] Updating index data for '{website_indexation.website.domain}'\n")
        else:
            self.stdout.write(f"[*] Updating remaining index data for '{website_indexation.website.domain}'\n")

        sliced_website_pages = indexed_page_urls[start_from:(start_from + self.QPD)]
        total_pages = len(sliced_website_pages)
        executor = ThreadPoolExecutor(max_workers=min(max(1, self.QPM // 12), total_pages))

        self.stdout.write(f"[*] Inspecting {total_pages} URLs.\n")

        for res, url in executor.map(fetch_url_inspection_details, itertools.repeat(credentials),
                                     itertools.repeat(site), sliced_website_pages):
            if not res:
                self.stdout.write(f"[X] Failed to inspect '{url}'\n")
                continue

            if res['inspectionResult']['indexStatusResult']['coverageState'] == "Submitted and indexed":
                indexed_pages_details[start_from]['indexed'] = True
                indexed_pages_details[start_from]['updated'] = True
                indexed_pages_details[start_from]['sent_for_indexing'] = False
            else:
                indexed_pages_details[start_from]['indexed'] = False
                indexed_pages_details[start_from]['updated'] = True

            start_from += 1

        # store the details in WebsiteIndexation
        website_indexation.indexation_details = indexed_pages_details
        website_indexation.save()

    def handle(self, *args, **options):
        users: QuerySet[User] = User.objects.filter(google_search_console_integrated=True)

        for user in users:
            self.stdout.write(f"[*] Running for '{user.email}'\n")

            try:
                credentials: Credentials = get_google_oauth2_credentials(user, "google-search-console")
            except RefreshError:
                self.stdout.write("[X] Access has been revoked by the user or the token has expired. Skipping...\n")
                continue

            if not credentials:
                self.stdout.write("[X] Error while fetching google oauth2 credentials. Skipping...\n")
                continue

            self.stdout.write("[*] Google oauth2 credentials fetched!\n")

            websites: QuerySet[Website] = user.website_set.all()
            for website in websites:
                try:
                    site = get_site_name_on_gsc(credentials, website.domain)
                    website_indexation, _ = WebsiteIndexation.objects.get_or_create(user=user, website=website, search_engine='google')
                    all_data_stored, start_from = self.all_indexed_pages_stored(website_indexation.indexation_details)

                    # Update website indexation `urls_sent_for_indexing` count
                    website_indexation.urls_sent_for_indexing = 0
                    website_indexation.save()

                    if not site:
                        self.stdout.write(f"[X] Failed to fetch GSC site for '{website.domain}'. Skipping...\n")
                        continue
                    else:
                        self.stdout.write(f"[*] GSC site fetched for '{website.domain}'\n")

                    if not all_data_stored and not website_indexation.completed:
                        all_urls = [page['url'] for page in website_indexation.indexation_details]
                        self.continue_store_indexing_details(credentials, site, all_urls, website_indexation, start_from)
                    else:
                        all_urls = self.fetch_website_urls(credentials, site)

                        # Add Abun articles
                        posted_abun_articles = user.article_set.filter(is_posted=True).values_list('article_link', flat=True).union(
                            user.howtoarticle_set.filter(is_posted=True).values_list('article_link', flat=True).union(
                                user.listicle_set.filter(is_posted=True).values_list('article_link', flat=True)
                            )
                        )
                        self.stdout.write(f"[*] {posted_abun_articles.count()} Abun articles URLs found.\n")
                        all_urls.extend(posted_abun_articles)

                        if not all_urls:
                            self.stdout.write("[X] Failed to fetch the URLs from sitemap and Abun articles. Skipping...")
                            website_indexation.completed = True
                            website_indexation.indexation_details = []
                            website_indexation.save()
                            continue

                        if not website_indexation.completed:
                            self.store_indexing_details(credentials, site, all_urls, website_indexation)
                        else:
                            self.update_indexing_details(credentials, site, all_urls, website_indexation)

                    # Refresh from the database
                    website_indexation.refresh_from_db()

                    all_data_stored, _ = self.all_indexed_pages_stored(website_indexation.indexation_details)
                    if all_data_stored:
                        self.stdout.write("[*] Done. All pages index data stored!\n")

                        if not website_indexation.completed:
                            self.stdout.write(f"[*] Sending email notification for '{website.domain}'\n")
                            email_message = website_indexation_process_completed_body(user.username, website.domain)
                            send_email(
                                user.email,
                                ABUN_NOTIFICATION_EMAIL,
                                "Team Abun",
                                "Website Indexing Process Completed!",
                                email_message
                            )

                            website_indexation.completed = True
                            website_indexation.save()

                    else:
                        self.stdout.write(f"[*] Done. {self.QPD} pages index data stored!\n")

                except HttpError as err:
                    reason = err._get_reason()
                    self.stdout.write(f"[X] HttpError -> '{reason}', Skipping...\n")
                    logger.error(f"HttpError: {reason}")
                
                except SSLError as err:
                    self.stdout.write(f"[X] SSLError -> '{err}', Skipping...\n")
                    logger.error(err)
                
                except MaxRetryError as err:
                    self.stdout.write(f"[X] MaxRetryError -> '{err}', Skipping...\n")
                    logger.error(err)

                except ConnectionError as err:
                    self.stdout.write(f"[X] ConnectionError -> '{err}', Skipping...\n")
                    logger.error(err)
                
                except Exception as err:
                    self.stdout.write(f"[X] Something went wrong! Skipping...\n")
                    logger.critical(err)
