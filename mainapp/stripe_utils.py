import time
import datetime
import hashlib
import logging
from functools import lru_cache
from typing import Dict, List, Tuple, Literal

import stripe

from django.db.models import QuerySet

from mainapp.decorators import dynamic_lru_cache
from AbunDRFBackend.settings import REDIS_STRIPE_DB
from mainapp.models import User, AutoCoupon, AppSumoLicense

logger = logging.getLogger('abun.frontend.pages')


def get_all_product_data(user: User, active=True, is_sorted=True) -> List[Dict]:
    """
    Returns all stripe product data with following datapoints:
    - id: string
    - name: string
    - metadata: Metadata
    - price_id: string
    - price_amount: int
    - currency_code: string ("usd"/"inr")

    Metadata:
    - description: string
    - position: int
    - max_articles: int
    - max_titles: int
    - websites: int
    - show: boolean
    - popular: boolean

    NOTE: products with metadata 'show'

    :param user: User model object.
    :param active: Only consider those prices that are active.
    :param is_sorted: Sort the data based on 'position' metadata
    :return: Stripe products data
    """
    plans: List[Dict] = []
    currency_code: str = 'inr' if user.country == "India" else 'usd'

    # Define plan-specific glossary words limits for Stripe products
    glossary_words_mapping = {
        "Trial": {"topics": 100, "glossary_words": 5},
        "Basic": {"topics": 100, "glossary_words": 20},
        "Pro": {"topics": 100, "glossary_words": 200},
        "Pro Max": {"topics": 400, "glossary_words": 1000},
    }

    # Define plan-specific gpf limits for Stripe products
    gpf_queries_mapping = {
        "Trial": 20,
        "Basic": 50,
        "Pro": 500,
        "Pro Max": 2000,
    }

    # Define plan-specific rpf limits for Stripe products
    rpf_queries_mapping = {
        "Trial": 20,
        "Basic": 50,
        "Pro": 500,
        "Pro Max": 2000,
    }

    for price in stripe.Price.list(active=active, expand=['data.product']):
        try:
            if (price['product']['metadata']['show'] == "true") and (price['currency'] == currency_code):
                glossary_plan = glossary_words_mapping.get(price['product']['name'], {"topics": 0, "glossary_words": 0})
                gpf_plan = gpf_queries_mapping.get(price['product']['name'], 0)
                rpf_plan = rpf_queries_mapping.get(price['product']['name'], 0)
                plans.append({
                    'id': price['product']['id'],
                    'name': price['product']['name'],
                    'metadata': {
                        'description': price['product']['metadata']['description'],
                        'position': int(price['product']['metadata']['position']),
                        'max_articles': int(price['product']['metadata']['max_articles']),
                        'max_titles': int(price['product']['metadata']['max_titles']),
                        'websites': int(price['product']['metadata']['websites']),
                        'show': price['product']['metadata']['show'] == "true",
                        'popular': price['product']['metadata']['popular'] == "true",
                        'max_keywords': int(price['product']['metadata']['max_keywords']) if 'max_keywords' in price['product']['metadata'] else 9999,
                        'max_emails': (
                            5 if price['product']['name'] == 'Trial' else
                            20 if price['product']['name'] == 'Basic' else
                            300 if price['product']['name'] == 'Pro' else
                            10000 if price['product']['name'] == 'Pro Max' else
                            0  # Default to 0 if no match
                        ),
                        'topics': glossary_plan["topics"],
                        'glossary_words': glossary_plan["glossary_words"], 
                    },
                    'price_id': price['id'],
                    'price_amount': price['unit_amount'],
                    'currency_code': currency_code,
                    'gpf_queries': gpf_plan, 
                    'rpf_queries': rpf_plan, 
                })
        except KeyError as k:
            logger.error(f"get_all_product_data() - Missing metadata {k} while fetching all product data")

    if is_sorted and plans:
        plans.sort(key=lambda p: p['metadata']['position'])

    return plans


def save_idempotency_key(idempotency_key: str):
    """
    Saves given idempotency key to Redis for 3 days.
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        redis_connection.set(idempotency_key, 1)
        redis_connection.expire(idempotency_key, 259200)


def generate_stripe_event_idempotency_key(usage: str, user_email: str, username: str) -> str:
    """
    Generates idempotency key for stripe events. Can be used in cases where we need to create some resource manually
    and want to use the idempotency key to prevent webhook code from running. The key is stored in Redis and expires
    after 1 hour.

    Ex. If we need to create stripe customer manually from our end instead of stripe creating one for us, we would be
    getting and saving stripe customer id in the same code/function. We don't want webhook to save the details
    again. So we check for this idempotency key on the webhook event and ignore the event if it matches.

    :param usage: Some string defining the event it's going to be used in.
    :param user_email: User email id.
    :param username: User account name.
    :returns: Idempotency key
    """
    h = hashlib.sha256()
    h.update(usage.encode('utf-8'))
    h.update(user_email.encode('utf-8'))
    h.update(username.encode('utf-8'))
    h.update(str(datetime.datetime.now().timestamp()).encode('utf-8'))
    idempotency_key = "abun_manual_event_" + h.hexdigest()

    save_idempotency_key(idempotency_key)

    return idempotency_key


def check_stripe_event_idempotency_key(idempotency_key: str) -> bool:
    """
    Checks if given idempotency_key is present in Redis. If so returns True
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        value = redis_connection.get(idempotency_key)

    return bool(value)


def get_stripe_product_data_by_name(plan_name: str) -> Dict:
    """
    Retrieves product data {id, name, metadata, inr, usd} from Stripe based on product name. The name is case-insensitive.

    Metadata contains all the different quotas or max limits for user's account for this plan.
    Metadata includes 'position', 'websites', 'max_articles' & 'max_titles'

    inr and usd contains 'id' & 'amount'.

    :param plan_name: Stripe product name (case-insensitive)
    :returns: Product data - product id (str), metadata (dict), inr pricing (int) & used pricing (int)
    """
    products = stripe.Product.list(active=True)
    try:
        product = list(filter(lambda p: p['name'].lower() == plan_name.lower(), products))[0]
    except IndexError:
        raise Exception(f"No plan with name {plan_name} was found")

    metadata = product['metadata'].to_dict()
    product_data = {
        'id': product['id'],
        'name': product['name'],
        'metadata': {
            'max_articles': int(metadata['max_articles']),
            'max_titles': int(metadata['max_titles']),
            'position': int(metadata['position']),
            'websites': int(metadata['websites']),
            'max_keywords': int(metadata['max_keywords']) if 'max_keywords' in metadata else 9999,
            'max_emails': (
                5 if product['name'] == 'Trial' else
                20 if product['name'] == 'Basic' else
                300 if product['name'] == 'Pro' else
                10000 if product['name'] == 'Pro Max' else
                0
            )
        }
    }
    for price in stripe.Price.list(product=product, expand=['data.currency_options']):
        product_data.update({
            f"{price['currency']}": {
                "id": price['id'],
                "amount": price['unit_amount']
            }
        })

    return product_data



def get_stripe_product_data_by_id(product_id: str):
    """
    Retrieves product data {id, name, metadata, inr, usd} from Stripe based on product id.

    Metadata contains all the different quotas or max limits for user's account for this plan.
    Metadata includes 'position', 'websites', 'max_articles' & 'max_titles'.

    inr and usd contains 'id' & 'amount'.

    :param product_id: Stripe product id.
    :returns: Product data - product id (str), name (str), metadata (dict), inr (dict) & usd (dict)
    """
    product = stripe.Product.retrieve(product_id)
    metadata = product['metadata'].to_dict()
    product_data = {
        'id': product['id'],
        'name': product['name'],
        'metadata': {
            'max_articles': int(metadata['max_articles']),
            'max_titles': int(metadata['max_titles']),
            'position': int(metadata['position']),
            'websites': int(metadata['websites']),
            'max_keywords': int(metadata['max_keywords']) if 'max_keywords' in metadata else 9999,
            'max_emails': (
                5 if product['name'] == 'Trial' else
                20 if product['name'] == 'Basic' else
                300 if product['name'] == 'Pro' else
                10000 if product['name'] == 'Pro Max' else
                0
            )
        }
    }
    for price in stripe.Price.list(product=product, expand=['data.currency_options']):
        product_data.update({
            f"{price['currency']}": {
                "id": price['id'],
                "amount": price['unit_amount']
            }
        })

    return product_data


def get_stripe_product_data(user: User):
    """
    Retrieves product data {id, name, metadata, inr, usd} from Stripe based on product id.

    Metadata contains all the different quotas or max limits for user's account for this plan.
    Metadata includes 'position', 'websites', 'max_articles' & 'max_titles'.

    inr and usd contains 'id' & 'amount'.

    :param user: User model instance
    :returns: Product data - product id (str), name (str), metadata (dict), inr (dict) & usd (dict)
    """
    def get_ltd_plans_combined_limit(appsumo_licenses: QuerySet[AppSumoLicense],
                                     limit_for: Literal["website", "keyword", "title", "article"]):
        """
        Returns LTD plan combined limit for `websites`, `keyworrds`, `titles` and `articles`.
        :param user: User model instance
        :param limit_for: Limit for `websites`, `keyworrd`, `title` and `article`.
        """
        if limit_for == 'title':
            # TODO: Update it later
            return 9999

        # Get the combined limit
        ltd_total_limit = sum(getattr(apppsumo_license, f"{limit_for}_limit") for apppsumo_license in appsumo_licenses.all())
        return ltd_total_limit

    # Define plan-specific glossary words limits for Stripe products
    glossary_words_mapping = {
        "Trial": {"topics": 100, "glossary_words": 5},
        "Basic": {"topics": 100, "glossary_words": 20},
        "Pro": {"topics": 100, "glossary_words": 200},
        "Pro Max": {"topics": 400, "glossary_words": 1000},
    }

    # Define plan-specific gpf limits for Stripe products
    gpf_queries_mapping = {
        "Trial": 20,
        "Basic": 50,
        "Pro": 500,
        "Pro Max": 2000,
    }

    # Define plan-specific rpf limits for Stripe products
    rpf_queries_mapping = {
        "Trial": 20,
        "Basic": 50,
        "Pro": 500,
        "Pro Max": 2000,
    }

    # Define plan-specific blog finder limits for Stripe products
    blog_finder_queries_mapping = {
        "Trial": 5,
        "Basic": 20,
        "Pro": 300,
        "Pro Max": 10000,
    }

    try:
        # Fetch the product
        product = stripe.Product.retrieve(user.stripe_product_id)
    except stripe.error.InvalidRequestError as err:
        logger.error(f"Error fetching product: {err}")

        # Try fetching the product again after 5 seconds
        time.sleep(5)
        product = stripe.Product.retrieve(user.stripe_product_id)

    metadata = product['metadata'].to_dict()
    glossary_plan = glossary_words_mapping.get(product['name'], {"topics": 0, "glossary_words": 0})
    gpf_plan = gpf_queries_mapping.get(product['name'], 0)
    rpf_plan = rpf_queries_mapping.get(product['name'], 0)
    blog_finder_plan = blog_finder_queries_mapping.get(product['name'], 0)

    # Fetch active lts plans
    appsumo_licenses = user.active_ltd_plans

    if appsumo_licenses.exists():
        product_data = {
            'id': "ltd",
            'name': "LTD",
            'display_name': f"LTD and {product['name']}",
            'metadata': {
                'position': 1,
                'max_articles': get_ltd_plans_combined_limit(appsumo_licenses, "article") + \
                                    (product['name'] != "Trial" and int(metadata['max_articles']) or 0),
                'max_titles': get_ltd_plans_combined_limit(appsumo_licenses, "title") + \
                                (product['name'] != "Trial" and int(metadata['max_titles'])),
                'websites': get_ltd_plans_combined_limit(appsumo_licenses, "website") + \
                                (product['name'] != "Trial" and int(metadata['websites'])),
                'max_keywords': get_ltd_plans_combined_limit(appsumo_licenses, "keyword") + \
                                   ( product['name'] != "Trial" and ('max_keywords' in metadata and int(metadata['max_keywords']) or 9999) or 0),
                'max_emails': 20 + (product['name'] != "Trial" and blog_finder_plan or 0),
                'topics': 100 + (product['name'] != "Trial" and glossary_plan["topics"] or 0),
                'glossary_words': 20 + (product['name'] != "Trial" and glossary_plan["glossary_words"] or 0),
                'gpf_queries': 50 + (product['name'] != "Trial" and gpf_plan or 0),
                'rpf_queries': 50 + (product['name'] != "Trial" and rpf_plan or 0),
            }
        }

    else:
        product_data = {
            'id': product['id'],
            'name': product['name'],
            'display_name': product['name'],
            'metadata': {
                'max_articles': int(metadata['max_articles']),
                'max_titles': int(metadata['max_titles']),
                'position': int(metadata['position']),
                'websites': int(metadata['websites']),
                'max_keywords': int(metadata['max_keywords']) if 'max_keywords' in metadata else 9999,
                'max_emails': blog_finder_plan,
                'topics': glossary_plan["topics"],
                'glossary_words': glossary_plan["glossary_words"],
                'gpf_queries': gpf_plan,
                'rpf_queries': rpf_plan,
            }
        }

    for price in stripe.Price.list(product=product, expand=['data.currency_options']):
        product_data.update({
            f"{price['currency']}": {
                "id": price['id'],
                "amount": price['unit_amount']
            }
        })

    return product_data


def latest_invoice_is_open(stripe_customer_id: str) -> bool:
    """
    Checks if latest invoice has 'open' status. If so returns True

    :param stripe_customer_id:
    :return:
    """
    invoices = stripe.Invoice.list(customer=stripe_customer_id)

    try:
        return invoices['data'][0]['status'] == 'open'
    except IndexError:
        return False


def create_checkout_session(user: User, price_id: str, success_url: str, cancel_url: str) -> str:
    """
    Starts a new checkout session, saves session id to user model and returns session url.

    Uses existing stripe customer id if present. Otherwise, if it's None, stripe will create a new customer
    with user's email id.

    :param user: User model object.
    :param price_id: Stripe price id.
    :param success_url: Stripe will redirect users here after successful payment.
    :param cancel_url: Stripe will redirect users here if they use the checkout back button.
    :returns: checkout session url.
    """
    try:
        coupon = AutoCoupon.objects.filter(enabled=True)
        discount_code = coupon[0].coupon_code
    except Exception as err:
        logger.error("No discount_code found--", err)
        discount_code = ""

    if user.stripe_customer_id:
        if discount_code:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                discounts= [{
                    "coupon": discount_code, 
                }],
                success_url=success_url,
                cancel_url=cancel_url,
                customer=user.stripe_customer_id,
                payment_method_types=['card'],
                billing_address_collection='required',
            )
        else:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                success_url=success_url,
                cancel_url=cancel_url,
                allow_promotion_codes=True,
                customer=user.stripe_customer_id,
                payment_method_types=['card'],
                billing_address_collection='required',
            )
    else:
        if discount_code:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                discounts= [{
                    "coupon": discount_code, 
                }],
                success_url=success_url,
                cancel_url=cancel_url,
                customer_email=user.email,
                payment_method_types=['card'],
                billing_address_collection='required',
            )
        else:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                success_url=success_url,
                cancel_url=cancel_url,
                allow_promotion_codes=True,
                customer_email=user.email,
                payment_method_types=['card'],
                billing_address_collection='required',
            )

    user.stripe_active_checkout_session_id = stripe_session['id']
    user.save()

    # Mark as first-time purchase in Redis
    mark_as_first_time_purchase(user.email)

    return stripe_session['url']


@dynamic_lru_cache()
def fetch_all_stripe_products_id_and_name(limit: int = 100) -> Tuple[List, List]:
    """
    Fetches all stripe products id and name
    :param limit: No. of products to fetch in a single request (optional)
    """
    products = []
    last_customer_id = None
    has_more = True

    while has_more:
        response = stripe.Product.list(
            limit=limit,
            starting_after=last_customer_id
        )

        products.extend(response.data)
        has_more = response.has_more

        if has_more:
            last_customer_id = response.data[-1].id

    products_id = []
    products_name = []

    for product in products:
        products_id.append(product.id)
        products_name.append(product.name)

    return products_id, products_name


def fetch_all_stripe_products(limit: int = 100) -> List:
    """
    Fetches all stripe products
    :param limit: No. of products to fetch in a single request (optional)
    """
    products = []
    last_customer_id = None
    has_more = True

    while has_more:
        response = stripe.Product.list(
            limit=limit,
            starting_after=last_customer_id
        )

        products.extend(response.data)
        has_more = response.has_more

        if has_more:
            last_customer_id = response.data[-1].id

    products_dict = []

    for product in products:
        metadata = product['metadata'].to_dict()
        product_data = {
            'id': product['id'],
            'name': product['name'],
            'metadata': {
                'max_articles': int(metadata['max_articles']) if 'max_articles' in metadata else 'Not Found',
                'max_titles': int(metadata['max_titles']) if 'max_titles' in metadata else 'Not Found',
                'position': int(metadata['position'])  if 'position' in metadata else 'Not Found',
                'websites': int(metadata['websites']) if 'websites' in metadata else 'Not Found',
                'max_keywords': int(metadata['max_keywords']) if 'max_keywords' in metadata else 9999,
                'max_emails': (
                    5 if product['name'] == 'Trial' else
                    20 if product['name'] == 'Basic' else
                    300 if product['name'] == 'Pro' else
                    10000 if product['name'] == 'Pro Max' else
                    0
                )
                
            }
        }

        products_dict.append(product_data)

    return products_dict


@lru_cache(typed=True, maxsize=50)
def get_user_plan_name_by_product_id(product_id: str):
    """
    Returns the user plan name by product id
    :paran product_id: 
    """
    if not product_id:
        return "Not Found"

    products_id, products_name = fetch_all_stripe_products_id_and_name()

    try:
        return products_name[products_id.index(product_id)]
    except (IndexError, ValueError):
        return "Not Found"


def mark_as_first_time_purchase(email: str, expiry_seconds: int = 259200):
    """
    Mark a user as making their first purchase attempt with a Redis key.
    Keys expire after 3 days (259200 seconds) by default.
    
    :param email: User's email address
    :param expiry_seconds: Time in seconds until the key expires
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    redis_key = f"first_time_purchase:{email}"
    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        redis_connection.set(redis_key, 1)
        redis_connection.expire(redis_key, expiry_seconds)


def is_first_time_purchase(email: str) -> bool:
    """
    Check if this is the user's first purchase attempt.
    
    :param email: User's email address
    :return: True if this is the first purchase, False otherwise
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    redis_key = f"first_time_purchase:{email}"
    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        return bool(redis_connection.get(redis_key))


def clear_first_time_purchase_flag(email: str):
    """
    Clear the first-time purchase flag for a user.
    
    :param email: User's email address
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    redis_key = f"first_time_purchase:{email}"
    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        redis_connection.delete(redis_key)


def cancel_trial_subscription_if_needed(user: User, user_prev_product_id: str, user_prev_sub_id: str, trial_plan: Dict):
    """
    Cancel the user's trial subscription if they're upgrading from a trial plan on their first purchase.
    
    :param user: User model object
    :param user_prev_product_id: Previous product ID
    :param user_prev_sub_id: Previous subscription ID
    :param trial_plan: Trial plan data
    """
    if user_prev_product_id != trial_plan['id']:
        return  # Only proceed if previous plan was a trial

    if not is_first_time_purchase(user.email):
        return  # Only proceed if this is their first purchase

    # Generate idempotency key for cancellation
    free_plan_cancellation_ik = generate_stripe_event_idempotency_key(
        "free_plan_cancellation", user.email, user.username
    )

    try:
        # Retrieve and cancel the previous subscription
        prev_sub = stripe.Subscription.retrieve(user_prev_sub_id)

        if prev_sub['status'] != 'canceled':
            stripe.Subscription.delete(user_prev_sub_id, idempotency_key=free_plan_cancellation_ik)
            logger.info(f"Canceled trial subscription {user_prev_sub_id} for user {user.email}")
        else:
            logger.info(f"Trial subscription {user_prev_sub_id} is already canceled.")

    except stripe.error.InvalidRequestError:
        logger.error(f"Failed to cancel trial subscription: No such subscription '{user_prev_sub_id}'")

    except Exception as e:
        logger.critical(f"Unexpected error canceling trial subscription: {str(e)}")

    # Clear the first-time purchase flag
    clear_first_time_purchase_flag(user.email)


def get_user_plan_names_batch(user_product_ids: List[str]) -> Dict[str, str]:
    """
    Returns a dictionary mapping product_ids to plan names
    :param user_product_ids: List of stripe product IDs
    :return: Dictionary mapping product_ids to plan names
    """
    # Filter out None values
    valid_product_ids = [pid for pid in user_product_ids if pid]
    
    if not valid_product_ids:
        return {}
    
    products_id, products_name = fetch_all_stripe_products_id_and_name()
    
    # Create mapping of product_id to name
    product_name_map = {}
    for pid in valid_product_ids:
        try:
            index = products_id.index(pid)
            product_name_map[pid] = products_name[index]
        except (IndexError, ValueError):
            product_name_map[pid] = "Not Found"

    return product_name_map


def get_user_subscription_history(user: User):
    """
    Get user subscription history
    """
    subscription_history = []

    if user.stripe_customer_id:
        subscriptions = stripe.Subscription.list(customer=user.stripe_customer_id, status='all')

        for sub in subscriptions['data']:
            product_id: str = sub['items']['data'][0]['price']['product']
            product_data: Dict = get_stripe_product_data_by_id(product_id)

            # Retrieve the latest invoice for the subscription
            invoices = stripe.Invoice.list(subscription=sub['id'], limit=1)
            latest_invoice = invoices.data[0] if invoices.data else None

            if latest_invoice:
                amount_paid = latest_invoice.amount_paid
            else:
                amount_paid = sub['items']['data'][0]['price']['unit_amount']

            subscription_history.append({
                    'active': sub['canceled_at'] is None,
                    'subscription_id': sub['id'],
                    'plan_name': product_data['name'],
                    'currency': sub['items']['data'][0]['price']['currency'],
                    'amount': amount_paid,
                    'created': datetime.datetime.fromtimestamp(
                        sub['created']
                    ).strftime("%d %b %Y"),
                    'created_date': datetime.datetime.fromtimestamp(
                        sub['created']
                    ).date(),
                    'current_period_start': datetime.datetime.fromtimestamp(
                        sub['current_period_start']
                    ).strftime("%d %b %Y"),
                    'current_period_end': datetime.datetime.fromtimestamp(
                        sub['current_period_end']
                    ).strftime("%d %b %Y"),
                })

    if user.appsumo_licenses.exists():
        for appsumo_license in user.appsumo_licenses.all():
            subscription_history.append({
                    'active': appsumo_license.license_status == "active",
                    'subscription_id': f"appsumo-ltd-id-{appsumo_license.id}",
                    'plan_name': f"Appsumo Tier {appsumo_license.tier}",
                    'currency': "usd",
                    # Stripe plans are priced in multiples of 100, so we need to multiply the price of Appsumo LTD plan by 100 as well.
                    'amount': appsumo_license.plan_amount_int * 100,
                    'created': appsumo_license.created_on.strftime("%d %b %Y"),
                    'created_date': appsumo_license.created_on.date(),
                    'current_period_start': None,
                    'current_period_end': None,
                })

    # Sort the subscription based on created date
    subscription_history = sorted(subscription_history, key=lambda subscriptoin: subscriptoin['created_date'], reverse=True)

    return subscription_history


def setup_free_plan_for_ltd_user(user_email: str, country: str):
    """
    Used to setup the free plan for LTD plan users
    :param user_email: User email
    :parma country: Country
    """
    # Fetch the user model instance
    try:
        user = User.objects.get(email=user_email)
    except User.DoesNotExist:
        logger.critical(f"No user found with '{user_email}' email")
        return None

    # Fetch the price id
    product_data = get_stripe_product_data_by_name("Trial")

    if country.lower() == "india":
        stripe_price_id = product_data["inr"]["id"]
    else:
        stripe_price_id = product_data["usd"]["id"]

    # Get stripe customer id by registering this user on stripe if not already present.
    if not user.stripe_customer_id:
        stripe_price = stripe.Price.retrieve(stripe_price_id)
        customer_creation_ik: str = generate_stripe_event_idempotency_key(
            "customer creation", user.email, user.password
        )

        if stripe_price.currency == "inr":
            customer = stripe.Customer.create(
                email=user.email,
                name=user.username,
                idempotency_key=customer_creation_ik,
                address={
                    "city": f"'{user.email}' city",
                    "country": "india",
                    "line1": f"'{user.email}' address line1",
                    "line2": f"'{user.email}' address line2",
                    "postal_code": f"'{user.email}' postal code",
                    "state": f"'{user.email}' state"
                }
            )
        else:
            customer = stripe.Customer.create(
                email=user.email,
                name=user.username,
                idempotency_key=customer_creation_ik
            )

        stripe_customer_id = customer['id']
        user.stripe_customer_id = stripe_customer_id
        user.save()

    else:
        stripe_customer_id = user.stripe_customer_id

    free_plan_subscription_ik = generate_stripe_event_idempotency_key(
        "free plan subscription",
        user.email,
        user.username
    )

    subscription = stripe.Subscription.create(
        customer=stripe_customer_id,
        items=[
            {'price': stripe_price_id}
        ],
        idempotency_key=free_plan_subscription_ik
    )

    user.stripe_subscription_id = subscription['id']
    user.stripe_pricing_id = subscription['items']['data'][0]['price']['id']
    user.stripe_product_id = subscription['items']['data'][0]['price']['product']
    user.save()
