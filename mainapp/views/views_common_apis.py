import os
import json
import logging
from typing import Dict

from django.core.handlers.wsgi import WSGIRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.clickjacking import xframe_options_exempt
from django.http import JsonResponse, HttpResponse, StreamingHttpResponse

import openai
import tldextract
import cryptography.fernet
from rest_framework.decorators import api_view
from rest_framework.request import Request

from AbunDRFBackend import settings
from mainapp.utils import decrypt_dict
from mainapp.models import ChangeLog, AICalculator, AIStreamingToken
from mainapp.serializers import ChangeLogSerializer
from mainapp.json_responses import JsonResponseBadRequest


logger = logging.getLogger(__name__)

@api_view(['GET'])
def get_changelogs(request: Request):
    """
    Get Changelogs.

    :param request: Django Rest Framework's Request object.
    """
    return JsonResponse(
        status=200,
        data=ChangeLogSerializer(ChangeLog.objects.all().order_by("-created_at"), many=True).data,
        safe=False,
    )


@api_view(['GET'])
def get_embed_code_loading_script(request: Request):
    """
    API view to generate a customized JavaScript for loading embed script.
    :param request: Django Rest Framework's Request object.
    """
    try:
        # Read the template script file
        script_path = os.path.join(settings.BASE_DIR, 'static', 'js', 'embed_code_loading_script.js')
        
        # Verify file exists
        if not os.path.exists(script_path):
            logger.error(f"Script file not found at {script_path}")
            # Return JavaScript error rather than HTML
            return HttpResponse(
                "console.error('Script file not found on server');", 
                content_type='application/javascript'
            )
            
        with open(script_path, 'r') as file:
            script_content = file.read()

        # Replace placeholders securely
        server_url: str = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
        script_content = script_content.replace('SERVER_URL', f"{server_url}/api/frontend/get-calculator-embed-script/")

        # Set content type and additional headers
        response = HttpResponse(script_content, content_type='application/javascript')
        
        # Add cache-control and CORS headers
        response['Cache-Control'] = 'max-age=3600'
        
        # Set CORS headers to allow any domain to load this script
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        
        return response
        
    except Exception as e:
        # Log the error but return a JavaScript response
        logger.error(f"Error serving script: {str(e)}")
        return HttpResponse(
            f"console.error('Error loading calculator script: {str(e)}');", 
            content_type='application/javascript'
        )


@api_view(['OPTIONS', 'GET'])
def get_calculator_embed_script(request: Request):
    """
    API view to generate a customized JavaScript embed script for a specific calculator.
    :param request: Django Rest Framework's Request object.
    """
    encrypted_data = request.query_params.get('data-calc', None)
    origin_domain = tldextract.extract(request.query_params.get('domain', '')).registered_domain

    # Validate the request has required parameters
    if not all([encrypted_data, origin_domain]) :
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS"})

    try:
        script_data = decrypt_dict(encrypted_data)
        calculator_id = script_data['CALCULATOR_ID']
        button_color = script_data.get('BUTTON_COLOR', '#007bff')
        url_restriction = script_data.get('URL_RESTRICTION', None)
    except (KeyError, ValueError, cryptography.fernet.InvalidToken):
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_DATA"})

    try:
        ai_calculator = AICalculator.objects.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Verify the calculator is ready to be embedded
    if not ai_calculator.is_verified:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_VERIFIED"})

    # Verify domain matches allowed domain
    allowed_domain = tldextract.extract(ai_calculator.url_restriction).registered_domain

    if not allowed_domain == origin_domain:
        logger.error(f"Unauthorized domain access attempt: {origin_domain}")
        return JsonResponseBadRequest(additional_data={'err_id': "UNAUTHORIZED_DOMAIN"})

    # Read the template script file
    script_path = os.path.join(settings.BASE_DIR, 'static', 'js', 'calculator_embed.js')
    with open(script_path, 'r') as file:
        script_content = file.read()

    # Replace placeholders
    server_url = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
    script_content = script_content.replace('CALCULATOR_ID', calculator_id)
    script_content = script_content.replace('SERVER_URL', server_url)
    script_content = script_content.replace('BUTTON_COLOR', button_color)
    script_content = script_content.replace('URL_RESTRICTION', url_restriction)

    # Set up the response with the proper CORS headers
    response = HttpResponse(script_content, content_type='application/javascript')
    response['Access-Control-Allow-Origin'] = origin_domain
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response['Access-Control-Allow-Headers'] = 'Content-Type'
    response['Cache-Control'] = 'max-age=3600'

    return response


@api_view(['GET'])
@xframe_options_exempt
def get_calculator_code(request: Request, calculator_id: str):
    """
    API view to get the calculator code
    :param request: Django Rest Framework's Request object.
    :param calculator_id: The unique ID of the calculator to retrieve
    """
    # Validate calculator_id format
    if not calculator_id or not isinstance(calculator_id, str) or len(calculator_id) > 100:
        logger.error(f"Invalid calculator ID format: {calculator_id}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_CALCULATOR_ID"})

    # Get the origin domain for CORS validation
    origin = request.headers.get('Origin')
    domain_param = request.query_params.get('domain')
    client_domain = origin or domain_param

    try:
        ai_calculator: AICalculator = AICalculator.objects.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        logger.error(f"No calculator found with {calculator_id} calculator ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Check if the calculator is verified
    if not ai_calculator.is_verified:
        logger.error(f"Calculator {calculator_id} is not verified.")
        return JsonResponseBadRequest(additional_data={'err_id': "CALCULATOR_NOT_VERIFIED"})

    # Check if the calculator is verified
    if not ai_calculator.is_verified:
        logger.error(f"Calculator {calculator_id} is not verified.")
        return JsonResponseBadRequest(additional_data={'err_id': "CALCULATOR_NOT_VERIFIED"})

    # Domain restriction check
    if ai_calculator.url_restriction and client_domain:
        # Check if the client domain is allowed
        if not client_domain.startswith(ai_calculator.url_restriction):
            logger.error(f"Domain restriction violated. Calculator {calculator_id}" \
                         f"is restricted to {ai_calculator.url_restriction}. Request from {client_domain}")
            return JsonResponseBadRequest(additional_data={'err_id': "UNAUTHORIZED_DOMAIN"})

    # Get the html code
    html_code: str = ai_calculator.html_code

    return HttpResponse(html_code, content_type="text/html")


@csrf_exempt
def stream_ai_response(request: WSGIRequest, token: str):
    """
    Streams the AI response
    :param request: Django Rest Framework's Request object
    :param token: Valid AI streaming token
    """
    if request.method == "POST":
        try:
            ai_streaming_token = AIStreamingToken.objects.get(token=token)
        except AIStreamingToken.DoesNotExist:
            return JsonResponseBadRequest(additional_data={"err_id": 'INVALID_TOKEN', "message": "Invalid token"})

        if not ai_streaming_token.is_valid():
            return JsonResponseBadRequest(additional_data={"err_id": 'EXPIRED_TOKEN', "message": "Token expired or invalid"})

        # get the payload from the body
        payload: Dict = json.loads(request.body.decode())

        # update the prompt if its a translation request
        translation_matching_prompt = """
        请帮我翻译以上内容，在翻译之前，想先判断一下这个内容是不是中文，如果是中文，则翻译问英文，如果是其他语言，则需要翻译为中文，注意，你只需要返回翻译的结果，不需要对此进行任何解释，不需要除了翻译结果以外的其他任何内容
        """.replace("\n", "").strip()

        updated_prompt = """
        Please help me translate the above content. Before translating, I want to determine whether the content is in English (US). If it is in English (US), translate it into English (UK). If it is in other languages, translate it into English (US). Please note that you only need to return the translation result, and you do not need to explain it or any other content except the translation result.
        """.replace("\n", "").strip()

        if translation_matching_prompt in payload['messages'][0]['content']:
            payload['messages'][0]['content'] = payload['messages'][0]['content'].replace(translation_matching_prompt, updated_prompt)

        def generate():
            client = openai.Client()
            # Create the streaming completion with the new API
            stream = client.chat.completions.create(**payload)

            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield f"data: {json.dumps({'choices': [{'delta': {'content': chunk.choices[0].delta.content}}]})}\n\n"

        # mark the token as used
        ai_streaming_token.used = True
        ai_streaming_token.save()

        return StreamingHttpResponse(generate(), content_type="text/event-stream")

    return JsonResponseBadRequest(additional_data={"message": "Requested method is not allowed"})
