(function (w, d, s, o) {
    // Unique ID for the calculator
    var calculatorId = o.calculatorId || 'default';
    var urlRestriction = o.urlRestriction || null;
    var containerId = 'custom-calculator-container-' + calculatorId;

    // Check if current url matches the restriction
    if (urlRestriction !== null) {
        var currentUrl = window.location.href;

        // Only proceed if the current url matches the restriction
        // Allow exact match or directory prefix
        if (currentUrl !== urlRestriction &&
            !currentUrl.startsWith(urlRestriction + '/')) {
            console.log("Calculator restricted to url: " + urlRestriction + ", current url: " + currentUrl);
            return;
        }
    }

    // Create container div for the calculator
    var container = d.createElement('div');
    container.id = containerId;
    container.style.position = 'fixed';
    container.style.bottom = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    d.body.appendChild(container);

    // Create the toggle button
    var toggleBtn = d.createElement('div');
    toggleBtn.id = 'calculator-toggle-btn';
    toggleBtn.innerHTML = '📊'; // Calculator icon
    toggleBtn.style.width = '60px';
    toggleBtn.style.height = '60px';
    toggleBtn.style.borderRadius = '50%';
    toggleBtn.style.backgroundColor = o.buttonColor || '#007bff';
    toggleBtn.style.color = '#ffffff';
    toggleBtn.style.display = 'flex';
    toggleBtn.style.justifyContent = 'center';
    toggleBtn.style.alignItems = 'center';
    toggleBtn.style.fontSize = '24px';
    toggleBtn.style.cursor = 'pointer';
    toggleBtn.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    container.appendChild(toggleBtn);

    // Create the calculator container (initially hidden)
    var calculatorContainer = d.createElement('div');
    calculatorContainer.id = 'calculator-widget-' + calculatorId;
    calculatorContainer.style.display = 'none';
    calculatorContainer.style.position = 'absolute';
    calculatorContainer.style.bottom = '70px';
    calculatorContainer.style.right = '0';
    calculatorContainer.style.width = '350px';
    calculatorContainer.style.height = '450px';
    calculatorContainer.style.backgroundColor = '#ffffff';
    calculatorContainer.style.borderRadius = '10px';
    calculatorContainer.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
    calculatorContainer.style.overflow = 'hidden';
    container.appendChild(calculatorContainer);

    // Create iframe to load the calculator
    var iframe = d.createElement('iframe');
    iframe.src = o.serverUrl + '/api/frontend/get-calculator-code/' + calculatorId;
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    calculatorContainer.appendChild(iframe);

    // Add close button
    var closeBtn = d.createElement('div');
    closeBtn.innerHTML = '✕';
    closeBtn.style.position = 'absolute';
    closeBtn.style.top = '10px';
    closeBtn.style.right = '10px';
    closeBtn.style.width = '20px';
    closeBtn.style.height = '20px';
    closeBtn.style.borderRadius = '50%';
    closeBtn.style.backgroundColor = '#f1f1f1';
    closeBtn.style.display = 'flex';
    closeBtn.style.justifyContent = 'center';
    closeBtn.style.alignItems = 'center';
    closeBtn.style.fontSize = '12px';
    closeBtn.style.cursor = 'pointer';
    calculatorContainer.appendChild(closeBtn);

    // Toggle calculator visibility
    toggleBtn.addEventListener('click', function () {
        if (calculatorContainer.style.display === 'none') {
            calculatorContainer.style.display = 'block';
        } else {
            calculatorContainer.style.display = 'none';
        }
    });

    // Close calculator when clicking close button
    closeBtn.addEventListener('click', function () {
        calculatorContainer.style.display = 'none';
    });

    // Expose API for external control
    w.CustomCalculator = w.CustomCalculator || {};
    w.CustomCalculator[calculatorId] = {
        open: function () {
            calculatorContainer.style.display = 'block';
        },
        close: function () {
            calculatorContainer.style.display = 'none';
        },
        toggle: function () {
            if (calculatorContainer.style.display === 'none') {
                calculatorContainer.style.display = 'block';
            } else {
                calculatorContainer.style.display = 'none';
            }
        }
    };

    // Allow communication from iframe to parent
    w.addEventListener('message', function (event) {
        // Verify origin for security
        if (event.origin !== o.serverUrl) return;

        if (event.data.action === 'close') {
            calculatorContainer.style.display = 'none';
        }
    });

})(window, document, 'script', {
    calculatorId: 'CALCULATOR_ID',
    serverUrl: 'SERVER_URL',
    buttonColor: 'BUTTON_COLOR',
    urlRestriction: 'URL_RESTRICTION'
});